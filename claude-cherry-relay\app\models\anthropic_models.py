"""
Anthropic格式的数据模型
"""
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel


class AnthropicMessage(BaseModel):
    """Anthropic消息格式"""
    role: str  # "user", "assistant"
    content: Union[str, List[Dict[str, Any]]]


class AnthropicChatRequest(BaseModel):
    """Anthropic聊天请求格式"""
    model: str
    max_tokens: int
    messages: List[AnthropicMessage]
    system: Optional[str] = None
    temperature: Optional[float] = None
    top_p: Optional[float] = None
    stop_sequences: Optional[List[str]] = None
    stream: Optional[bool] = False


class AnthropicUsage(BaseModel):
    """Anthropic使用统计"""
    input_tokens: int
    output_tokens: int


class AnthropicChatResponse(BaseModel):
    """Anthropic聊天响应格式"""
    id: str
    type: str = "message"
    role: str = "assistant"
    content: List[Dict[str, Any]]
    model: str
    stop_reason: Optional[str] = None
    stop_sequence: Optional[str] = None
    usage: AnthropicUsage


class AnthropicStreamEvent(BaseModel):
    """Anthropic流式事件"""
    type: str
    message: Optional[Dict[str, Any]] = None
    content_block: Optional[Dict[str, Any]] = None
    delta: Optional[Dict[str, Any]] = None
    usage: Optional[AnthropicUsage] = None


class AnthropicError(BaseModel):
    """Anthropic错误格式"""
    type: str
    message: str
