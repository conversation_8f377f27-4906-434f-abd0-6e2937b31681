"""
API格式转换器
OpenAI格式 ↔ Anthropic Messages API格式
"""
import json
import time
import uuid
from typing import List, Dict, Any, Optional, Tuple

from app.models.openai_models import (
    OpenAIChatRequest, OpenAIChatResponse, OpenAIMessage, 
    OpenAIChoice, OpenAIUsage, OpenAIStreamResponse, OpenAIStreamChoice
)
from app.models.anthropic_models import (
    AnthropicChatRequest, AnthropicMessage, AnthropicChatResponse
)
from app.core.logging import logger


class FormatConverter:
    """API格式转换器"""
    
    @staticmethod
    def openai_to_anthropic(openai_request: OpenAIChatRequest) -> AnthropicChatRequest:
        """将OpenAI格式转换为Anthropic格式"""
        
        # 提取系统消息
        system_message = None
        messages = []
        
        for msg in openai_request.messages:
            if msg.role == "system":
                system_message = msg.content
            elif msg.role in ["user", "assistant"]:
                messages.append(AnthropicMessage(
                    role=msg.role,
                    content=msg.content
                ))
        
        # 设置默认max_tokens（Anthropic必需参数）
        max_tokens = openai_request.max_tokens or 4096
        
        # 构建Anthropic请求
        anthropic_request = AnthropicChatRequest(
            model=openai_request.model,
            max_tokens=max_tokens,
            messages=messages,
            system=system_message,
            temperature=openai_request.temperature,
            top_p=openai_request.top_p,
            stream=openai_request.stream
        )
        
        # 处理stop参数
        if openai_request.stop:
            if isinstance(openai_request.stop, str):
                anthropic_request.stop_sequences = [openai_request.stop]
            else:
                anthropic_request.stop_sequences = openai_request.stop
        
        logger.debug(f"转换OpenAI请求到Anthropic: {anthropic_request.model}")
        return anthropic_request
    
    @staticmethod
    def anthropic_to_openai(
        anthropic_response: AnthropicChatResponse,
        original_model: str
    ) -> OpenAIChatResponse:
        """将Anthropic响应转换为OpenAI格式"""
        
        # 提取文本内容
        content = ""
        if anthropic_response.content:
            for block in anthropic_response.content:
                if block.get("type") == "text":
                    content += block.get("text", "")
        
        # 构建OpenAI响应
        openai_response = OpenAIChatResponse(
            id=anthropic_response.id,
            created=int(time.time()),
            model=original_model,
            choices=[
                OpenAIChoice(
                    index=0,
                    message=OpenAIMessage(
                        role="assistant",
                        content=content
                    ),
                    finish_reason=FormatConverter._map_stop_reason(
                        anthropic_response.stop_reason
                    )
                )
            ],
            usage=OpenAIUsage(
                prompt_tokens=anthropic_response.usage.input_tokens,
                completion_tokens=anthropic_response.usage.output_tokens,
                total_tokens=(
                    anthropic_response.usage.input_tokens + 
                    anthropic_response.usage.output_tokens
                )
            )
        )
        
        logger.debug(f"转换Anthropic响应到OpenAI: {anthropic_response.id}")
        return openai_response
    
    @staticmethod
    def anthropic_stream_to_openai(
        event_data: Dict[str, Any],
        request_id: str,
        original_model: str
    ) -> Optional[str]:
        """将Anthropic流式事件转换为OpenAI SSE格式"""
        
        event_type = event_data.get("type")
        
        if event_type == "message_start":
            # 开始消息
            response = OpenAIStreamResponse(
                id=request_id,
                created=int(time.time()),
                model=original_model,
                choices=[
                    OpenAIStreamChoice(
                        index=0,
                        delta={"role": "assistant", "content": ""},
                        finish_reason=None
                    )
                ]
            )
            return f"data: {response.json()}\n\n"
        
        elif event_type == "content_block_delta":
            # 内容增量
            delta = event_data.get("delta", {})
            if delta.get("type") == "text_delta":
                text = delta.get("text", "")
                response = OpenAIStreamResponse(
                    id=request_id,
                    created=int(time.time()),
                    model=original_model,
                    choices=[
                        OpenAIStreamChoice(
                            index=0,
                            delta={"content": text},
                            finish_reason=None
                        )
                    ]
                )
                return f"data: {response.json()}\n\n"
        
        elif event_type == "message_stop":
            # 消息结束
            response = OpenAIStreamResponse(
                id=request_id,
                created=int(time.time()),
                model=original_model,
                choices=[
                    OpenAIStreamChoice(
                        index=0,
                        delta={},
                        finish_reason="stop"
                    )
                ]
            )
            return f"data: {response.json()}\n\ndata: [DONE]\n\n"
        
        return None
    
    @staticmethod
    def _map_stop_reason(anthropic_stop_reason: Optional[str]) -> Optional[str]:
        """映射停止原因"""
        mapping = {
            "end_turn": "stop",
            "max_tokens": "length",
            "stop_sequence": "stop"
        }
        return mapping.get(anthropic_stop_reason, "stop")
    
    @staticmethod
    def create_error_response(
        error_message: str,
        error_type: str = "api_error",
        error_code: Optional[str] = None
    ) -> Dict[str, Any]:
        """创建错误响应"""
        return {
            "error": {
                "message": error_message,
                "type": error_type,
                "code": error_code
            }
        }
