# Claude Cherry Relay Service 使用指南

## 快速开始

### 1. 配置环境变量

设置你的Claude Code API信息：

```bash
# Linux/macOS
export ANTHROPIC_BASE_URL="https://co.yes.vg"
export ANTHROPIC_AUTH_TOKEN="cr_c834de4b0211387a8a481a81b2cbfca3192fe3c16579b718fd664d3c25446970"

# Windows
set ANTHROPIC_BASE_URL=https://co.yes.vg
set ANTHROPIC_AUTH_TOKEN=cr_c834de4b0211387a8a481a81b2cbfca3192fe3c16579b718fd664d3c25446970
```

### 2. 检查配置

运行配置检查脚本：

```bash
python check_config.py
```

这会验证：
- Python版本
- 依赖包安装
- 环境变量配置
- Claude API连接
- 文件权限

### 3. 启动服务

```bash
# 使用启动脚本（推荐）
./start.sh          # Linux/macOS
start.bat           # Windows

# 或手动启动
python main.py
```

### 4. 测试服务

```bash
# 运行API测试
python test_api.py

# 运行使用示例
python example_usage.py
```

## Cherry Studio配置

1. 打开Cherry Studio
2. 进入设置 → API配置
3. 添加新的API配置：
   - **名称**: Claude Cherry Relay
   - **API类型**: OpenAI Compatible
   - **Base URL**: `http://localhost:8000/v1`
   - **API Key**: 任意字符串（会透传给Claude Code API）
4. 选择模型：`claude-sonnet-4-20250514`

## 支持的模型

- `claude-sonnet-4-20250514`

## API端点

### 聊天完成
```
POST /v1/chat/completions
```

请求格式（OpenAI兼容）：
```json
{
  "model": "claude-sonnet-4-20250514",
  "messages": [
    {"role": "user", "content": "Hello!"}
  ],
  "max_tokens": 100,
  "temperature": 0.7,
  "stream": false
}
```

### 模型列表
```
GET /v1/models
```

### 健康检查
```
GET /health
```

## 高级配置

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `ANTHROPIC_BASE_URL` | `https://co.yes.vg` | Claude Code API地址 |
| `ANTHROPIC_AUTH_TOKEN` | - | Claude Code API密钥 |
| `HOST` | `0.0.0.0` | 服务监听地址 |
| `PORT` | `8000` | 服务端口 |
| `LOG_LEVEL` | `INFO` | 日志级别 |
| `REQUEST_TIMEOUT` | `300` | 请求超时（秒） |
| `HTTP_PROXY` | - | HTTP代理 |
| `HTTPS_PROXY` | - | HTTPS代理 |

### 代理配置

如果需要通过代理访问Claude API：

```bash
export HTTP_PROXY=http://proxy.example.com:8080
export HTTPS_PROXY=https://proxy.example.com:8080
```

## Docker部署

### 使用docker-compose（推荐）

1. 创建 `.env` 文件：
```bash
ANTHROPIC_BASE_URL=https://co.yes.vg
ANTHROPIC_AUTH_TOKEN=your-token-here
```

2. 启动服务：
```bash
docker-compose up -d
```

### 直接使用Docker

```bash
docker build -t claude-cherry-relay .

docker run -d \
  --name claude-cherry-relay \
  -p 8000:8000 \
  -e ANTHROPIC_BASE_URL="https://co.yes.vg" \
  -e ANTHROPIC_AUTH_TOKEN="your-token-here" \
  claude-cherry-relay
```

## 故障排除

### 常见错误

1. **401 Unauthorized**
   - 检查 `ANTHROPIC_AUTH_TOKEN` 是否正确
   - 确认Claude Code API密钥有效

2. **连接超时**
   - 检查网络连接
   - 确认 `ANTHROPIC_BASE_URL` 可访问
   - 考虑配置代理

3. **模型不支持**
   - 使用支持的模型列表中的模型
   - 检查Claude Code API是否支持该模型

4. **流式响应问题**
   - 确认客户端支持SSE
   - 检查网络代理设置

### 日志查看

```bash
# 查看实时日志
tail -f logs/app.log

# 查看错误日志
grep ERROR logs/app.log

# 查看特定时间的日志
grep "2024-01-01" logs/app.log
```

## 性能优化

1. **调整超时设置**：根据网络情况调整 `REQUEST_TIMEOUT`
2. **使用代理**：如果直连不稳定，配置HTTP代理
3. **监控日志**：定期检查日志文件，及时发现问题
4. **资源监控**：监控CPU和内存使用情况

## 安全建议

1. **网络安全**：不要将服务直接暴露到公网
2. **API密钥**：妥善保管Claude Code API密钥
3. **访问控制**：使用防火墙限制访问
4. **日志管理**：定期清理日志文件
5. **更新维护**：及时更新依赖包
