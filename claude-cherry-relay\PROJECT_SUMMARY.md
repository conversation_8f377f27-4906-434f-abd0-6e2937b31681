# Claude Cherry Relay Service - 项目总结

## 项目概述

这是一个专门为将Claude Code API转换为Cherry Studio兼容格式而创建的中继服务。该服务接收OpenAI格式的API请求，将其转换为Anthropic Messages API格式，调用您的Claude Code API，然后将响应转换回OpenAI格式返回给Cherry Studio。

## 核心功能

### ✅ API格式转换
- **输入**: OpenAI格式的聊天请求
- **输出**: Anthropic Messages API格式
- **返回**: OpenAI格式的响应
- **支持**: 普通对话和流式对话

### ✅ 支持的模型
- `claude-sonnet-4-20250514` (唯一支持的模型)

### ✅ 完整功能支持
- 系统消息处理
- 多轮对话历史
- 流式响应 (Server-Sent Events)
- 错误处理和日志记录
- 健康检查和监控

## 项目结构

```
claude-cherry-relay/
├── main.py                    # 主服务器入口
├── app/
│   ├── api/
│   │   └── chat.py           # 聊天API路由
│   ├── core/
│   │   ├── config.py         # 配置管理
│   │   └── logging.py        # 日志配置
│   ├── services/
│   │   ├── claude_client.py  # Claude API客户端
│   │   └── format_converter.py # 格式转换器
│   └── models/
│       ├── openai_models.py  # OpenAI格式模型
│       └── anthropic_models.py # Anthropic格式模型
├── test_api.py               # API测试脚本
├── check_config.py           # 配置检查脚本
├── example_usage.py          # 使用示例
├── requirements.txt          # Python依赖
├── Dockerfile               # Docker配置
├── docker-compose.yml       # Docker Compose配置
├── start.sh / start.bat     # 启动脚本
└── 文档文件
```

## 快速开始

### 1. 环境配置
```bash
export ANTHROPIC_BASE_URL="https://co.yes.vg"
export ANTHROPIC_AUTH_TOKEN="cr_c834de4b0211387a8a481a81b2cbfca3192fe3c16579b718fd664d3c25446970"
```

### 2. 启动服务
```bash
# Linux/macOS
./start.sh

# Windows
start.bat

# 或手动启动
python main.py
```

### 3. Cherry Studio配置
- **API Base URL**: `http://localhost:8000/v1`
- **模型**: `claude-sonnet-4-20250514`
- **API Key**: 任意字符串

## 技术特点

### 🚀 高性能
- 基于FastAPI构建
- 异步处理请求
- 支持并发连接

### 🔒 安全可靠
- 完整的错误处理
- 详细的日志记录
- 健康检查端点

### 🐳 易于部署
- Docker支持
- 跨平台启动脚本
- 环境变量配置

### 🔧 易于维护
- 模块化设计
- 清晰的代码结构
- 完整的测试脚本

## API端点

| 端点 | 方法 | 说明 |
|------|------|------|
| `/v1/chat/completions` | POST | 聊天完成API |
| `/v1/models` | GET | 获取支持的模型列表 |
| `/health` | GET | 健康检查 |
| `/` | GET | 服务信息 |

## 测试验证

项目包含完整的测试套件：

1. **配置检查**: `python check_config.py`
2. **API测试**: `python test_api.py`
3. **使用示例**: `python example_usage.py`

## 部署选项

### 本地部署
```bash
python main.py
```

### Docker部署
```bash
docker-compose up -d
```

### 生产环境
建议使用反向代理（Nginx）和进程管理器（systemd/PM2）

## 监控和维护

### 日志文件
- 位置: `logs/app.log`
- 轮转: 10MB，保留5个文件
- 级别: 可配置（INFO/DEBUG/ERROR）

### 健康检查
- 端点: `http://localhost:8000/health`
- 返回: 服务状态和时间戳

## 故障排除

常见问题和解决方案已在 `USAGE_GUIDE.md` 中详细说明。

## 许可证

MIT License - 可自由使用和修改

---

**注意**: 此项目专门为您的Claude Code API (`https://co.yes.vg`) 和特定的认证令牌设计，仅支持 `claude-sonnet-4-20250514` 模型。
