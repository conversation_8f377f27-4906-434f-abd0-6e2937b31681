# Claude Cherry Relay Service

一个简单的API中继服务，将Claude Code API转换为Cherry Studio兼容的OpenAI格式。

## 功能特性

- 🔄 **API格式转换**：OpenAI格式 ↔ Anthropic Messages API格式
- 🚀 **流式响应**：支持Server-Sent Events (SSE)流式传输
- 🔐 **安全认证**：支持API密钥认证
- 📝 **完整日志**：详细的请求和错误日志
- ⚡ **高性能**：基于FastAPI构建，异步处理
- 🐳 **容器化**：支持Docker部署

## 快速开始

### 环境要求

- Python 3.8+
- 有效的Claude Code API密钥和URL

### 安装和配置

1. **克隆项目**
```bash
git clone <repository-url>
cd claude-cherry-relay
```

2. **配置环境变量**
```bash
# 复制配置文件
cp .env.example .env

# 编辑 .env 文件，设置你的Claude Code API信息
# 或者直接设置环境变量：
export ANTHROPIC_BASE_URL="https://co.yes.vg"
export ANTHROPIC_AUTH_TOKEN="cr_c834de4b0211387a8a481a81b2cbfca3192fe3c16579b718fd664d3c25446970"
```

3. **启动服务**

**方式一：使用启动脚本（推荐）**
```bash
# Linux/macOS
chmod +x start.sh
./start.sh

# Windows
start.bat
```

**方式二：手动启动**
```bash
# 安装依赖
pip install -r requirements.txt

# 启动服务
python main.py
```

服务将在 `http://localhost:8000` 启动。

### Cherry Studio配置

在Cherry Studio中配置API：
- **API Base URL**: `http://localhost:8000/v1`
- **API Key**: 任意字符串（会透传给Claude Code API）
- **模型**: `claude-sonnet-4-20250514`

### 测试服务

运行测试脚本验证服务是否正常工作：
```bash
python test_api.py
```

测试包括：
- 健康检查
- 模型列表获取
- 普通聊天对话
- 流式聊天对话
- 系统消息处理
- 错误处理

## API端点

### 聊天完成
```
POST /v1/chat/completions
```

支持OpenAI格式的聊天完成请求，包括：
- 普通文本对话
- 流式响应 (`stream: true`)
- 系统消息
- 多轮对话历史

### 模型列表
```
GET /v1/models
```

返回支持的Claude模型列表。

## 项目结构

```
claude-cherry-relay/
├── main.py              # 主服务器入口
├── app/
│   ├── __init__.py
│   ├── api/
│   │   ├── __init__.py
│   │   └── chat.py      # 聊天API路由
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py    # 配置管理
│   │   └── logging.py   # 日志配置
│   ├── services/
│   │   ├── __init__.py
│   │   ├── claude_client.py    # Claude API客户端
│   │   └── format_converter.py # 格式转换器
│   └── models/
│       ├── __init__.py
│       ├── openai_models.py    # OpenAI格式模型
│       └── anthropic_models.py # Anthropic格式模型
├── requirements.txt     # Python依赖
├── Dockerfile          # Docker配置
├── .env.example        # 环境变量示例
└── README.md          # 项目文档
```

## 部署

### Docker部署

```bash
# 构建镜像
docker build -t claude-cherry-relay .

# 运行容器
docker run -d \
  --name claude-cherry-relay \
  -p 8000:8000 \
  -e ANTHROPIC_BASE_URL="https://co.yes.vg" \
  -e ANTHROPIC_AUTH_TOKEN="your-token-here" \
  claude-cherry-relay
```

### 生产环境

建议使用反向代理（如Nginx）和进程管理器（如PM2或systemd）来部署生产环境。

## 故障排除

### 常见问题

1. **连接Claude API失败**
   - 检查 `ANTHROPIC_BASE_URL` 和 `ANTHROPIC_AUTH_TOKEN` 是否正确
   - 确认网络连接正常，可以访问Claude API
   - 查看日志文件 `logs/app.log` 获取详细错误信息

2. **Cherry Studio连接失败**
   - 确认服务正在运行：访问 `http://localhost:8000/health`
   - 检查Cherry Studio中的API Base URL是否正确
   - 确认防火墙没有阻止8000端口

3. **流式响应问题**
   - 确认客户端支持Server-Sent Events (SSE)
   - 检查网络代理设置，某些代理可能会缓冲流式响应

### 日志查看

```bash
# 查看实时日志
tail -f logs/app.log

# 查看错误日志
grep ERROR logs/app.log
```

## 许可证

MIT License
