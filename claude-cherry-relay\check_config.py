"""
配置检查脚本
验证环境配置是否正确
"""
import os
import sys
import asyncio
import httpx
from app.core.config import get_settings


def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python版本过低: {version.major}.{version.minor}.{version.micro}")
        print("   需要Python 3.8或更高版本")
        return False


def check_environment_variables():
    """检查环境变量"""
    print("\n🔍 检查环境变量...")
    
    settings = get_settings()
    issues = []
    
    # 检查必需的环境变量
    if not settings.anthropic_auth_token:
        issues.append("ANTHROPIC_AUTH_TOKEN 未设置")
    else:
        print(f"✅ ANTHROPIC_AUTH_TOKEN: {settings.anthropic_auth_token[:10]}...")
    
    if not settings.anthropic_base_url:
        issues.append("ANTHROPIC_BASE_URL 未设置")
    else:
        print(f"✅ ANTHROPIC_BASE_URL: {settings.anthropic_base_url}")
    
    # 显示其他配置
    print(f"✅ 服务地址: {settings.host}:{settings.port}")
    print(f"✅ 日志级别: {settings.log_level}")
    print(f"✅ 请求超时: {settings.request_timeout}s")
    
    if settings.http_proxy:
        print(f"✅ HTTP代理: {settings.http_proxy}")
    if settings.https_proxy:
        print(f"✅ HTTPS代理: {settings.https_proxy}")
    
    if issues:
        print("\n❌ 环境变量问题:")
        for issue in issues:
            print(f"   - {issue}")
        return False
    
    return True


async def check_claude_api_connection():
    """检查Claude API连接"""
    print("\n🔍 检查Claude API连接...")
    
    settings = get_settings()
    
    # 配置代理
    proxies = {}
    if settings.http_proxy:
        proxies["http://"] = settings.http_proxy
    if settings.https_proxy:
        proxies["https://"] = settings.https_proxy
    
    # 测试连接
    headers = {
        "Authorization": f"Bearer {settings.anthropic_auth_token}",
        "Content-Type": "application/json",
        "User-Agent": "claude-cherry-relay/1.0.0"
    }
    
    test_request = {
        "model": "claude-sonnet-4-20250514",
        "max_tokens": 10,
        "messages": [
            {"role": "user", "content": "Hi"}
        ]
    }
    
    try:
        async with httpx.AsyncClient(
            timeout=30.0,
            proxies=proxies if proxies else None
        ) as client:
            response = await client.post(
                f"{settings.anthropic_base_url.rstrip('/')}/v1/messages",
                headers=headers,
                json=test_request
            )
            
            if response.status_code == 200:
                print("✅ Claude API连接成功")
                data = response.json()
                print(f"   响应ID: {data.get('id', 'unknown')}")
                return True
            else:
                print(f"❌ Claude API连接失败: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   错误信息: {error_data.get('error', {}).get('message', 'Unknown')}")
                except:
                    print(f"   响应内容: {response.text}")
                return False
                
    except httpx.RequestError as e:
        print(f"❌ 网络连接错误: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {str(e)}")
        return False


def check_dependencies():
    """检查依赖包"""
    print("\n🔍 检查依赖包...")
    
    required_packages = [
        "fastapi",
        "uvicorn",
        "httpx",
        "pydantic",
        "python-dotenv"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} (未安装)")
    
    if missing_packages:
        print(f"\n❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("   请运行: pip install -r requirements.txt")
        return False
    
    return True


def check_file_permissions():
    """检查文件权限"""
    print("\n🔍 检查文件权限...")
    
    # 检查日志目录
    log_dir = "logs"
    if not os.path.exists(log_dir):
        try:
            os.makedirs(log_dir)
            print(f"✅ 创建日志目录: {log_dir}")
        except Exception as e:
            print(f"❌ 无法创建日志目录: {str(e)}")
            return False
    else:
        print(f"✅ 日志目录存在: {log_dir}")
    
    # 检查写入权限
    test_file = os.path.join(log_dir, "test.tmp")
    try:
        with open(test_file, "w") as f:
            f.write("test")
        os.remove(test_file)
        print("✅ 日志目录写入权限正常")
    except Exception as e:
        print(f"❌ 日志目录写入权限问题: {str(e)}")
        return False
    
    return True


async def main():
    """运行所有检查"""
    print("🔧 Claude Cherry Relay Service 配置检查")
    print("=" * 50)
    
    checks = [
        ("Python版本", check_python_version()),
        ("依赖包", check_dependencies()),
        ("环境变量", check_environment_variables()),
        ("文件权限", check_file_permissions()),
        ("Claude API连接", await check_claude_api_connection())
    ]
    
    all_passed = True
    for check_name, result in checks:
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有检查通过！服务可以正常启动。")
        print("\n启动服务:")
        print("  python main.py")
        print("  或者运行: ./start.sh (Linux/macOS) 或 start.bat (Windows)")
    else:
        print("❌ 存在配置问题，请修复后重试。")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
