"""
聊天API路由
"""
import json
import time
import uuid
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import StreamingResponse
from fastapi.responses import JSONResponse

from app.models.openai_models import (
    OpenAIChatRequest, OpenAIChatResponse, OpenAIModelsResponse, 
    OpenAIModel, OpenAIErrorResponse, OpenAIError
)
from app.services.claude_client import ClaudeClient, ClaudeAPIError
from app.services.format_converter import FormatConverter
from app.core.logging import logger

router = APIRouter()

# 支持的Claude模型列表
SUPPORTED_MODELS = [
    {
        "id": "claude-sonnet-4-20250514",
        "object": "model",
        "created": int(time.time()),
        "owned_by": "anthropic"
    }
]


@router.get("/models")
async def list_models() -> OpenAIModelsResponse:
    """获取支持的模型列表"""
    logger.info("获取模型列表")
    
    models = [OpenAIModel(**model) for model in SUPPORTED_MODELS]
    return OpenAIModelsResponse(data=models)


@router.post("/chat/completions")
async def chat_completions(request: OpenAIChatRequest) -> Any:
    """聊天完成API"""
    
    logger.info(f"收到聊天请求: {request.model}, 流式: {request.stream}")
    
    try:
        # 转换请求格式
        anthropic_request = FormatConverter.openai_to_anthropic(request)
        
        # 创建Claude客户端
        async with ClaudeClient() as claude_client:
            
            if request.stream:
                # 流式响应
                return StreamingResponse(
                    _stream_chat_completion(claude_client, anthropic_request, request.model),
                    media_type="text/event-stream",
                    headers={
                        "Cache-Control": "no-cache",
                        "Connection": "keep-alive",
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Headers": "*",
                        "Access-Control-Allow-Methods": "*"
                    }
                )
            else:
                # 非流式响应
                anthropic_response = await claude_client.chat_completion(anthropic_request)
                openai_response = FormatConverter.anthropic_to_openai(
                    anthropic_response, request.model
                )
                
                logger.info(f"聊天完成: {openai_response.id}")
                return openai_response
                
    except ClaudeAPIError as e:
        logger.error(f"Claude API错误: {e.message}")
        error_response = OpenAIErrorResponse(
            error=OpenAIError(
                message=e.message,
                type=e.error_type,
                code=str(e.status_code)
            )
        )
        return JSONResponse(
            status_code=e.status_code,
            content=error_response.dict()
        )
    except Exception as e:
        logger.error(f"未知错误: {str(e)}")
        error_response = OpenAIErrorResponse(
            error=OpenAIError(
                message=f"内部服务器错误: {str(e)}",
                type="internal_error",
                code="500"
            )
        )
        return JSONResponse(
            status_code=500,
            content=error_response.dict()
        )


async def _stream_chat_completion(
    claude_client: ClaudeClient,
    anthropic_request,
    original_model: str
):
    """流式聊天完成生成器"""
    
    request_id = f"chatcmpl-{uuid.uuid4().hex}"
    
    try:
        async for event_data in claude_client.chat_completion_stream(anthropic_request):
            # 转换流式事件为OpenAI格式
            openai_chunk = FormatConverter.anthropic_stream_to_openai(
                event_data, request_id, original_model
            )
            
            if openai_chunk:
                yield openai_chunk
                
    except ClaudeAPIError as e:
        logger.error(f"流式请求错误: {e.message}")
        error_data = {
            "error": {
                "message": e.message,
                "type": e.error_type,
                "code": str(e.status_code)
            }
        }
        yield f"data: {json.dumps(error_data)}\n\n"
    except Exception as e:
        logger.error(f"流式请求未知错误: {str(e)}")
        error_data = {
            "error": {
                "message": f"内部服务器错误: {str(e)}",
                "type": "internal_error",
                "code": "500"
            }
        }
        yield f"data: {json.dumps(error_data)}\n\n"
