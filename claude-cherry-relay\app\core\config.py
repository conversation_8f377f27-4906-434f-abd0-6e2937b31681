"""
配置管理模块
"""
import os
from typing import Optional
from pydantic import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # Claude Code API配置
    anthropic_base_url: str = "https://co.yes.vg"
    anthropic_auth_token: str = ""
    
    # 服务器配置
    host: str = "0.0.0.0"
    port: int = 8000
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "logs/app.log"
    
    # 代理配置
    http_proxy: Optional[str] = None
    https_proxy: Optional[str] = None
    
    # 超时配置
    request_timeout: int = 300
    connect_timeout: int = 30
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# 全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings
