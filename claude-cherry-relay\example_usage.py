"""
使用示例：如何通过Python代码调用中继服务
"""
import asyncio
import httpx
import json


async def example_chat():
    """示例：普通聊天对话"""
    print("=== 普通聊天对话示例 ===")
    
    request_data = {
        "model": "claude-sonnet-4-20250514",
        "messages": [
            {"role": "user", "content": "你好！请介绍一下Python的优势。"}
        ],
        "max_tokens": 200,
        "temperature": 0.7
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/v1/chat/completions",
            json=request_data,
            timeout=60.0
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"助手回复: {data['choices'][0]['message']['content']}")
            print(f"Token使用: {data['usage']}")
        else:
            print(f"请求失败: {response.status_code} - {response.text}")


async def example_stream_chat():
    """示例：流式聊天对话"""
    print("\n=== 流式聊天对话示例 ===")
    
    request_data = {
        "model": "claude-sonnet-4-20250514",
        "messages": [
            {"role": "user", "content": "请写一个Python函数来计算斐波那契数列。"}
        ],
        "max_tokens": 300,
        "temperature": 0.7,
        "stream": True
    }
    
    async with httpx.AsyncClient() as client:
        async with client.stream(
            "POST",
            "http://localhost:8000/v1/chat/completions",
            json=request_data,
            timeout=60.0
        ) as response:
            
            if response.status_code == 200:
                print("助手回复: ", end="", flush=True)
                
                async for line in response.aiter_lines():
                    line = line.strip()
                    if not line or not line.startswith("data: "):
                        continue
                    
                    data_str = line[6:]  # 移除 "data: " 前缀
                    
                    if data_str == "[DONE]":
                        print("\n[流式响应完成]")
                        break
                    
                    try:
                        chunk_data = json.loads(data_str)
                        if chunk_data.get("choices"):
                            delta = chunk_data["choices"][0].get("delta", {})
                            if "content" in delta:
                                print(delta["content"], end="", flush=True)
                    except json.JSONDecodeError:
                        continue
            else:
                print(f"流式请求失败: {response.status_code} - {response.text}")


async def example_system_message():
    """示例：使用系统消息"""
    print("\n=== 系统消息示例 ===")
    
    request_data = {
        "model": "claude-sonnet-4-20250514",
        "messages": [
            {"role": "system", "content": "你是一个专业的代码审查员，请用简洁的语言回答。"},
            {"role": "user", "content": "这段代码有什么问题？\n\ndef add(a, b):\n    return a + b + 1"}
        ],
        "max_tokens": 150,
        "temperature": 0.3
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/v1/chat/completions",
            json=request_data,
            timeout=60.0
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"代码审查结果: {data['choices'][0]['message']['content']}")
        else:
            print(f"请求失败: {response.status_code} - {response.text}")


async def example_multi_turn():
    """示例：多轮对话"""
    print("\n=== 多轮对话示例 ===")
    
    # 模拟多轮对话历史
    messages = [
        {"role": "user", "content": "什么是递归？"},
        {"role": "assistant", "content": "递归是一种编程技术，函数调用自身来解决问题。它通常包含基础情况和递归情况。"},
        {"role": "user", "content": "能给我一个简单的递归例子吗？"}
    ]
    
    request_data = {
        "model": "claude-sonnet-4-20250514",
        "messages": messages,
        "max_tokens": 200,
        "temperature": 0.7
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/v1/chat/completions",
            json=request_data,
            timeout=60.0
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"助手回复: {data['choices'][0]['message']['content']}")
        else:
            print(f"请求失败: {response.status_code} - {response.text}")


async def main():
    """运行所有示例"""
    print("🚀 Claude Cherry Relay Service 使用示例")
    print("确保服务正在运行: http://localhost:8000")
    print("=" * 60)
    
    try:
        await example_chat()
        await example_stream_chat()
        await example_system_message()
        await example_multi_turn()
        
        print("\n" + "=" * 60)
        print("✅ 所有示例运行完成！")
        
    except Exception as e:
        print(f"\n❌ 运行示例时出错: {str(e)}")
        print("请确保服务正在运行并且配置正确。")


if __name__ == "__main__":
    asyncio.run(main())
