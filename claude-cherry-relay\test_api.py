"""
API测试脚本
"""
import asyncio
import json
import httpx
from typing import Dict, Any

# 测试配置
BASE_URL = "http://localhost:8000"
TEST_MODEL = "claude-sonnet-4-20250514"


async def test_health_check():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    
    async with httpx.AsyncClient() as client:
        response = await client.get(f"{BASE_URL}/health")
        
        if response.status_code == 200:
            print("✅ 健康检查通过")
            print(f"   响应: {response.json()}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            print(f"   响应: {response.text}")


async def test_models_list():
    """测试模型列表"""
    print("\n🔍 测试模型列表...")
    
    async with httpx.AsyncClient() as client:
        response = await client.get(f"{BASE_URL}/v1/models")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 模型列表获取成功")
            print(f"   支持的模型数量: {len(data['data'])}")
            for model in data['data']:
                print(f"   - {model['id']}")
        else:
            print(f"❌ 模型列表获取失败: {response.status_code}")
            print(f"   响应: {response.text}")


async def test_chat_completion():
    """测试聊天完成（非流式）"""
    print("\n🔍 测试聊天完成（非流式）...")
    
    request_data = {
        "model": TEST_MODEL,
        "messages": [
            {"role": "user", "content": "你好，请简单介绍一下你自己。"}
        ],
        "max_tokens": 100,
        "temperature": 0.7,
        "stream": False
    }
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        response = await client.post(
            f"{BASE_URL}/v1/chat/completions",
            json=request_data
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 聊天完成成功")
            print(f"   响应ID: {data['id']}")
            print(f"   模型: {data['model']}")
            print(f"   内容: {data['choices'][0]['message']['content'][:100]}...")
            print(f"   使用统计: {data['usage']}")
        else:
            print(f"❌ 聊天完成失败: {response.status_code}")
            print(f"   响应: {response.text}")


async def test_chat_completion_stream():
    """测试聊天完成（流式）"""
    print("\n🔍 测试聊天完成（流式）...")
    
    request_data = {
        "model": TEST_MODEL,
        "messages": [
            {"role": "user", "content": "请用一句话介绍Python编程语言。"}
        ],
        "max_tokens": 50,
        "temperature": 0.7,
        "stream": True
    }
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        async with client.stream(
            "POST",
            f"{BASE_URL}/v1/chat/completions",
            json=request_data
        ) as response:
            
            if response.status_code == 200:
                print("✅ 流式聊天开始")
                content_parts = []
                
                async for line in response.aiter_lines():
                    line = line.strip()
                    if not line:
                        continue
                    
                    if line.startswith("data: "):
                        data_str = line[6:]
                        
                        if data_str == "[DONE]":
                            print("\n✅ 流式聊天完成")
                            break
                        
                        try:
                            chunk_data = json.loads(data_str)
                            if chunk_data.get("choices"):
                                delta = chunk_data["choices"][0].get("delta", {})
                                if "content" in delta:
                                    content = delta["content"]
                                    content_parts.append(content)
                                    print(content, end="", flush=True)
                        except json.JSONDecodeError:
                            continue
                
                print(f"\n   完整内容: {''.join(content_parts)}")
            else:
                print(f"❌ 流式聊天失败: {response.status_code}")
                print(f"   响应: {await response.aread()}")


async def test_system_message():
    """测试系统消息"""
    print("\n🔍 测试系统消息...")
    
    request_data = {
        "model": TEST_MODEL,
        "messages": [
            {"role": "system", "content": "你是一个专业的Python编程助手。"},
            {"role": "user", "content": "什么是列表推导式？"}
        ],
        "max_tokens": 100,
        "temperature": 0.7,
        "stream": False
    }
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        response = await client.post(
            f"{BASE_URL}/v1/chat/completions",
            json=request_data
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 系统消息测试成功")
            print(f"   内容: {data['choices'][0]['message']['content'][:100]}...")
        else:
            print(f"❌ 系统消息测试失败: {response.status_code}")
            print(f"   响应: {response.text}")


async def test_error_handling():
    """测试错误处理"""
    print("\n🔍 测试错误处理...")
    
    # 测试无效模型
    request_data = {
        "model": "invalid-model",
        "messages": [
            {"role": "user", "content": "测试"}
        ],
        "max_tokens": 10
    }
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        response = await client.post(
            f"{BASE_URL}/v1/chat/completions",
            json=request_data
        )
        
        if response.status_code != 200:
            print("✅ 错误处理正常")
            print(f"   状态码: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   错误信息: {error_data.get('error', {}).get('message', 'Unknown')}")
            except:
                print(f"   响应: {response.text}")
        else:
            print("❌ 错误处理异常，应该返回错误")


async def main():
    """运行所有测试"""
    print("🚀 开始API测试")
    print(f"测试目标: {BASE_URL}")
    print(f"测试模型: {TEST_MODEL}")
    print("=" * 50)
    
    try:
        await test_health_check()
        await test_models_list()
        await test_chat_completion()
        await test_chat_completion_stream()
        await test_system_message()
        await test_error_handling()
        
        print("\n" + "=" * 50)
        print("🎉 所有测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
