#!/bin/bash

# Claude Cherry Relay Service 启动脚本

set -e

echo "=== Claude Cherry Relay Service 启动脚本 ==="

# 检查Python版本
python_version=$(python3 --version 2>&1 | cut -d' ' -f2 | cut -d'.' -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "错误: 需要Python $required_version 或更高版本，当前版本: $python_version"
    exit 1
fi

echo "✅ Python版本检查通过: $python_version"

# 检查环境变量
if [ -z "$ANTHROPIC_AUTH_TOKEN" ]; then
    echo "错误: 请设置 ANTHROPIC_AUTH_TOKEN 环境变量"
    echo "示例: export ANTHROPIC_AUTH_TOKEN='your-token-here'"
    exit 1
fi

echo "✅ 环境变量检查通过"

# 创建虚拟环境（如果不存在）
if [ ! -d "venv" ]; then
    echo "创建Python虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
echo "激活虚拟环境..."
source venv/bin/activate

# 安装依赖
echo "安装Python依赖..."
pip install -r requirements.txt

# 创建日志目录
mkdir -p logs

# 显示配置信息
echo ""
echo "=== 服务配置 ==="
echo "Claude API URL: ${ANTHROPIC_BASE_URL:-https://co.yes.vg}"
echo "服务地址: ${HOST:-0.0.0.0}:${PORT:-8000}"
echo "日志级别: ${LOG_LEVEL:-INFO}"
echo ""

# 启动服务
echo "启动Claude Cherry Relay Service..."
python main.py
