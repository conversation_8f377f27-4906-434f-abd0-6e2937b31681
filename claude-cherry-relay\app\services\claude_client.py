"""
Claude Code API客户端
"""
import json
import asyncio
from typing import Dict, Any, AsyncGenerator, Optional
import httpx
from httpx import AsyncClient, Response

from app.core.config import get_settings
from app.core.logging import logger
from app.models.anthropic_models import AnthropicChatRequest, AnthropicChatResponse


class Claude<PERSON>IError(Exception):
    """Claude API错误"""
    def __init__(self, message: str, status_code: int = 500, error_type: str = "api_error"):
        self.message = message
        self.status_code = status_code
        self.error_type = error_type
        super().__init__(message)


class ClaudeClient:
    """Claude Code API客户端"""
    
    def __init__(self):
        self.settings = get_settings()
        self.base_url = self.settings.anthropic_base_url.rstrip('/')
        self.auth_token = self.settings.anthropic_auth_token
        
        # 配置代理
        proxies = {}
        if self.settings.http_proxy:
            proxies["http://"] = self.settings.http_proxy
        if self.settings.https_proxy:
            proxies["https://"] = self.settings.https_proxy
        
        # 创建HTTP客户端
        self.client = AsyncClient(
            timeout=httpx.Timeout(
                connect=self.settings.connect_timeout,
                read=self.settings.request_timeout
            ),
            proxies=proxies if proxies else None
        )
        
        logger.info(f"初始化Claude客户端: {self.base_url}")
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    def _get_headers(self, stream: bool = False) -> Dict[str, str]:
        """获取请求头"""
        headers = {
            "Authorization": f"Bearer {self.auth_token}",
            "Content-Type": "application/json",
            "User-Agent": "claude-cherry-relay/1.0.0"
        }
        
        if stream:
            headers["Accept"] = "text/event-stream"
            headers["Cache-Control"] = "no-cache"
        
        return headers
    
    async def chat_completion(
        self, 
        request: AnthropicChatRequest
    ) -> AnthropicChatResponse:
        """发送聊天完成请求（非流式）"""
        
        url = f"{self.base_url}/v1/messages"
        headers = self._get_headers(stream=False)
        
        # 确保非流式请求
        request_data = request.dict(exclude_none=True)
        request_data["stream"] = False
        
        logger.info(f"发送Claude API请求: {request.model}")
        logger.debug(f"请求URL: {url}")
        logger.debug(f"请求数据: {json.dumps(request_data, indent=2)}")
        
        try:
            response = await self.client.post(
                url,
                headers=headers,
                json=request_data
            )
            
            await self._handle_response_errors(response)
            
            response_data = response.json()
            logger.info(f"Claude API响应成功: {response_data.get('id', 'unknown')}")
            
            return AnthropicChatResponse(**response_data)
            
        except httpx.RequestError as e:
            logger.error(f"Claude API请求失败: {str(e)}")
            raise ClaudeAPIError(f"网络请求失败: {str(e)}", 503, "network_error")
        except Exception as e:
            logger.error(f"Claude API未知错误: {str(e)}")
            raise ClaudeAPIError(f"未知错误: {str(e)}", 500, "unknown_error")
    
    async def chat_completion_stream(
        self, 
        request: AnthropicChatRequest
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """发送聊天完成请求（流式）"""
        
        url = f"{self.base_url}/v1/messages"
        headers = self._get_headers(stream=True)
        
        # 确保流式请求
        request_data = request.dict(exclude_none=True)
        request_data["stream"] = True
        
        logger.info(f"发送Claude API流式请求: {request.model}")
        logger.debug(f"请求URL: {url}")
        
        try:
            async with self.client.stream(
                "POST",
                url,
                headers=headers,
                json=request_data
            ) as response:
                
                await self._handle_response_errors(response)
                
                logger.info("开始接收Claude API流式响应")
                
                async for line in response.aiter_lines():
                    line = line.strip()
                    
                    if not line:
                        continue
                    
                    if line.startswith("data: "):
                        data_str = line[6:]  # 移除 "data: " 前缀
                        
                        if data_str == "[DONE]":
                            logger.info("Claude API流式响应结束")
                            break
                        
                        try:
                            event_data = json.loads(data_str)
                            yield event_data
                        except json.JSONDecodeError as e:
                            logger.warning(f"解析流式数据失败: {data_str}, 错误: {e}")
                            continue
                
        except httpx.RequestError as e:
            logger.error(f"Claude API流式请求失败: {str(e)}")
            raise ClaudeAPIError(f"网络请求失败: {str(e)}", 503, "network_error")
        except Exception as e:
            logger.error(f"Claude API流式未知错误: {str(e)}")
            raise ClaudeAPIError(f"未知错误: {str(e)}", 500, "unknown_error")
    
    async def _handle_response_errors(self, response: Response):
        """处理响应错误"""
        if response.status_code == 200:
            return
        
        try:
            error_data = response.json()
            error_message = error_data.get("error", {}).get("message", "未知错误")
            error_type = error_data.get("error", {}).get("type", "api_error")
        except:
            error_message = f"HTTP {response.status_code}: {response.text}"
            error_type = "http_error"
        
        logger.error(f"Claude API错误 [{response.status_code}]: {error_message}")
        
        # 映射HTTP状态码到错误类型
        if response.status_code == 401:
            error_type = "authentication_error"
        elif response.status_code == 403:
            error_type = "permission_error"
        elif response.status_code == 429:
            error_type = "rate_limit_error"
        elif response.status_code >= 500:
            error_type = "server_error"
        
        raise ClaudeAPIError(error_message, response.status_code, error_type)
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()
        logger.info("Claude客户端已关闭")
