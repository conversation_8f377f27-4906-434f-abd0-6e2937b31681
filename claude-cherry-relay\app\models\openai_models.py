"""
OpenAI格式的数据模型
"""
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel


class OpenAIMessage(BaseModel):
    """OpenAI消息格式"""
    role: str  # "system", "user", "assistant"
    content: str


class OpenAIChatRequest(BaseModel):
    """OpenAI聊天请求格式"""
    model: str
    messages: List[OpenAIMessage]
    temperature: Optional[float] = 1.0
    max_tokens: Optional[int] = None
    stream: Optional[bool] = False
    top_p: Optional[float] = 1.0
    frequency_penalty: Optional[float] = 0.0
    presence_penalty: Optional[float] = 0.0
    stop: Optional[Union[str, List[str]]] = None


class OpenAIUsage(BaseModel):
    """OpenAI使用统计"""
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int


class OpenAIChoice(BaseModel):
    """OpenAI选择项"""
    index: int
    message: OpenAIMessage
    finish_reason: Optional[str] = None


class OpenAIChatResponse(BaseModel):
    """OpenAI聊天响应格式"""
    id: str
    object: str = "chat.completion"
    created: int
    model: str
    choices: List[OpenAIChoice]
    usage: OpenAIUsage


class OpenAIStreamChoice(BaseModel):
    """OpenAI流式选择项"""
    index: int
    delta: Dict[str, Any]
    finish_reason: Optional[str] = None


class OpenAIStreamResponse(BaseModel):
    """OpenAI流式响应格式"""
    id: str
    object: str = "chat.completion.chunk"
    created: int
    model: str
    choices: List[OpenAIStreamChoice]


class OpenAIModel(BaseModel):
    """OpenAI模型信息"""
    id: str
    object: str = "model"
    created: int
    owned_by: str


class OpenAIModelsResponse(BaseModel):
    """OpenAI模型列表响应"""
    object: str = "list"
    data: List[OpenAIModel]


class OpenAIError(BaseModel):
    """OpenAI错误格式"""
    message: str
    type: str
    code: Optional[str] = None


class OpenAIErrorResponse(BaseModel):
    """OpenAI错误响应"""
    error: OpenAIError
