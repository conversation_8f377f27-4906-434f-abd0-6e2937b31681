@echo off
chcp 65001 >nul

echo === Claude Cherry Relay Service 启动脚本 ===

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo ✅ Python检查通过

REM 检查环境变量
if "%ANTHROPIC_AUTH_TOKEN%"=="" (
    echo 错误: 请设置 ANTHROPIC_AUTH_TOKEN 环境变量
    echo 示例: set ANTHROPIC_AUTH_TOKEN=your-token-here
    pause
    exit /b 1
)

echo ✅ 环境变量检查通过

REM 创建虚拟环境（如果不存在）
if not exist "venv" (
    echo 创建Python虚拟环境...
    python -m venv venv
)

REM 激活虚拟环境
echo 激活虚拟环境...
call venv\Scripts\activate.bat

REM 安装依赖
echo 安装Python依赖...
pip install -r requirements.txt

REM 创建日志目录
if not exist "logs" mkdir logs

REM 显示配置信息
echo.
echo === 服务配置 ===
if "%ANTHROPIC_BASE_URL%"=="" (
    echo Claude API URL: https://co.yes.vg
) else (
    echo Claude API URL: %ANTHROPIC_BASE_URL%
)
if "%HOST%"=="" (
    echo 服务地址: 0.0.0.0:8000
) else (
    echo 服务地址: %HOST%:%PORT%
)
if "%LOG_LEVEL%"=="" (
    echo 日志级别: INFO
) else (
    echo 日志级别: %LOG_LEVEL%
)
echo.

REM 启动服务
echo 启动Claude Cherry Relay Service...
python main.py

pause
