version: '3.8'

services:
  claude-cherry-relay:
    build: .
    container_name: claude-cherry-relay
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - ANTHROPIC_BASE_URL=${ANTHROPIC_BASE_URL:-https://co.yes.vg}
      - ANTHROPIC_AUTH_TOKEN=${ANTHROPIC_AUTH_TOKEN}
      - HOST=0.0.0.0
      - PORT=8000
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
